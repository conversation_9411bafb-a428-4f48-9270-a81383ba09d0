import { http } from "@/utils/http";
import type { Result } from "@/utils/response";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps } from "@/views/system/user/utils/type";

/*
 ***************************
 *   Read Operations (GET)
 ***************************
 */
export const getUsers = (params?: object) => {
  return http.request<Result>("get", "/api/auth/users", {
    params
  });
};

export const getUserById = (id: number) => {
  return http.request<Result>("get", `/api/auth/users/${id}`);
};

/*
 ***************************
 *   Create & Update Operations
 ***************************
 */
export const createUser = (data: FormItemProps) => {
  return http.request<Result>("post", "/api/auth/users", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateUserById = (id: number, data: FormItemProps) => {
  return http.request<Result>("put", `/api/auth/users/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Soft Delete Operations
 ***************************
 */
export const deleteUserById = (id: number) => {
  return http.request<Result>("delete", `/api/auth/users/${id}`);
};

export const bulkDeleteUsers = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/users/bulk-delete", {
    data
  });
};

/*
 ***************************
 *   Hard Delete Operations
 ***************************
 */
export const destroyUserById = (id: number) => {
  return http.request<Result>("delete", `/api/auth/users/${id}`);
};

export const bulkDestroyUsers = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/users/bulk-destroy", {
    data
  });
};

/*
 ***************************
 *   Restore Operations
 ***************************
 */
export const restoreUserById = (id: number) => {
  return http.request<Result>("put", `/api/auth/users/${id}/restore`);
};

export const bulkRestoreUsers = (data: { ids: number[] }) => {
  return http.request<Result>("put", "/api/auth/users/bulk-restore", {
    data
  });
};

/*
 ***************************
 *   Dropdown Operations
 ***************************
 */
export const dropdownUsers = () => {
  return http.request<Result>("get", "/api/auth/users/dropdown");
};
