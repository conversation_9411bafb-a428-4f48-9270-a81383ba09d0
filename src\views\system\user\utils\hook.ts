import { reactive, ref } from "vue";
import { useConvertKeyToCamel, useConvertKeyToSnake } from "@/utils/helpers";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";
import {
  getUsers,
  deleteUserById,
  bulkDeleteUsers,
  destroyUserById,
  bulkDestroyUsers,
  restoreUserById,
  bulkRestoreUsers,
  createUser,
  updateUserById
} from "./auth-api";
import type { UserFilterProps } from "./type";

export function useUserHook() {
  /*
   ***************************
   *   Data/State Management
   ***************************
   */
  const loading = ref(false);
  const filterRef = ref<UserFilterProps>({ isTrashed: "no" });
  const pagination = reactive({
    total: 0,
    pageSize: 20,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100, 200],
    background: true,
    layout: "total, sizes, prev, pager, next, jumper"
  });
  const multipleSelection = ref([]);
  const records = ref([]);
  const sort = ref({ sortBy: "createdAt", sortOrder: "asc" });
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref<FieldValues>({
    status: "active",
    isActive: true
  });
  const userFormRef = ref();

  /*
   ***************************
   *   API Handlers
   ***************************
   */
  const fnGetUsers = async () => {
    loading.value = true;
    try {
      const params = {
        page: pagination.currentPage,
        per_page: pagination.pageSize,
        sort_by: sort.value.sortBy,
        sort_order: sort.value.sortOrder,
        ...useConvertKeyToSnake(filterRef.value)
      };

      const { data } = await getUsers(params);
      const result = useConvertKeyToCamel(data);
      
      records.value = result.data || [];
      pagination.total = result.total || 0;
    } catch (error) {
      console.error("Error fetching users:", error);
      message($t("Failed to fetch users"), { type: "error" });
    } finally {
      loading.value = false;
    }
  };

  const fnHandleCreateUser = async (formData: FieldValues) => {
    try {
      await createUser(formData);
      message($t("User created successfully"), { type: "success" });
      drawerVisible.value = false;
      fnGetUsers();
    } catch (error) {
      console.error("Error creating user:", error);
      message($t("Failed to create user"), { type: "error" });
    }
  };

  const fnHandleUpdateUser = async (id: number, formData: FieldValues) => {
    try {
      await updateUserById(id, formData);
      message($t("User updated successfully"), { type: "success" });
      drawerVisible.value = false;
      fnGetUsers();
    } catch (error) {
      console.error("Error updating user:", error);
      message($t("Failed to update user"), { type: "error" });
    }
  };

  const fnHandleDelete = async (id: number) => {
    try {
      await deleteUserById(id);
      message($t("User deleted successfully"), { type: "success" });
      fnGetUsers();
    } catch (error) {
      console.error("Error deleting user:", error);
      message($t("Failed to delete user"), { type: "error" });
    }
  };

  const fnHandleBulkDelete = async (ids: number[]) => {
    try {
      await bulkDeleteUsers({ ids });
      message($t("Users deleted successfully"), { type: "success" });
      fnGetUsers();
    } catch (error) {
      console.error("Error bulk deleting users:", error);
      message($t("Failed to delete users"), { type: "error" });
    }
  };

  /*
   ***************************
   *   Table Event Handlers
   ***************************
   */
  const fnHandleSelectionChange = (selection: any[]) => {
    multipleSelection.value = selection;
  };

  const fnHandleSortChange = ({ prop, order }: any) => {
    sort.value.sortBy = prop || "createdAt";
    sort.value.sortOrder = order === "ascending" ? "asc" : "desc";
    fnGetUsers();
  };

  const fnHandlePageChange = (page: number) => {
    pagination.currentPage = page;
    fnGetUsers();
  };

  const fnHandleSizeChange = (size: number) => {
    pagination.pageSize = size;
    pagination.currentPage = 1;
    fnGetUsers();
  };

  /*
   ***************************
   *   UI Action Handlers
   ***************************
   */
  const handleDelete = (row: any) => {
    ElMessageBox.confirm(
      $t("Are you sure you want to delete this user?"),
      $t("Warning"),
      {
        confirmButtonText: $t("Confirm"),
        cancelButtonText: $t("Cancel"),
        type: "warning"
      }
    )
      .then(() => {
        fnHandleDelete(row.id);
      })
      .catch(() => {
        message($t("Delete cancelled"), { type: "info" });
      });
  };

  const handleBulkDelete = () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select users to delete"), { type: "warning" });
      return;
    }

    ElMessageBox.confirm(
      $t("Are you sure you want to delete selected users?"),
      $t("Warning"),
      {
        confirmButtonText: $t("Confirm"),
        cancelButtonText: $t("Cancel"),
        type: "warning"
      }
    )
      .then(() => {
        const ids = multipleSelection.value.map((item: any) => item.id);
        fnHandleBulkDelete(ids);
      })
      .catch(() => {
        message($t("Delete cancelled"), { type: "info" });
      });
  };

  const handleDestroy = (row: any) => {
    ElMessageBox.confirm(
      $t("Are you sure you want to permanently delete this user?"),
      $t("Warning"),
      {
        confirmButtonText: $t("Confirm"),
        cancelButtonText: $t("Cancel"),
        type: "warning"
      }
    )
      .then(async () => {
        try {
          await destroyUserById(row.id);
          message($t("User permanently deleted"), { type: "success" });
          fnGetUsers();
        } catch (error) {
          message($t("Failed to permanently delete user"), { type: "error" });
        }
      })
      .catch(() => {
        message($t("Delete cancelled"), { type: "info" });
      });
  };

  const handleBulkDestroy = () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select users to delete"), { type: "warning" });
      return;
    }

    ElMessageBox.confirm(
      $t("Are you sure you want to permanently delete selected users?"),
      $t("Warning"),
      {
        confirmButtonText: $t("Confirm"),
        cancelButtonText: $t("Cancel"),
        type: "warning"
      }
    )
      .then(async () => {
        try {
          const ids = multipleSelection.value.map((item: any) => item.id);
          await bulkDestroyUsers({ ids });
          message($t("Users permanently deleted"), { type: "success" });
          fnGetUsers();
        } catch (error) {
          message($t("Failed to permanently delete users"), { type: "error" });
        }
      })
      .catch(() => {
        message($t("Delete cancelled"), { type: "info" });
      });
  };

  const handleRestore = (row: any) => {
    ElMessageBox.confirm(
      $t("Are you sure you want to restore this user?"),
      $t("Confirm"),
      {
        confirmButtonText: $t("Confirm"),
        cancelButtonText: $t("Cancel"),
        type: "info"
      }
    )
      .then(async () => {
        try {
          await restoreUserById(row.id);
          message($t("User restored successfully"), { type: "success" });
          fnGetUsers();
        } catch (error) {
          message($t("Failed to restore user"), { type: "error" });
        }
      })
      .catch(() => {
        message($t("Restore cancelled"), { type: "info" });
      });
  };

  const handleBulkRestore = () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select users to restore"), { type: "warning" });
      return;
    }

    ElMessageBox.confirm(
      $t("Are you sure you want to restore selected users?"),
      $t("Confirm"),
      {
        confirmButtonText: $t("Confirm"),
        cancelButtonText: $t("Cancel"),
        type: "info"
      }
    )
      .then(async () => {
        try {
          const ids = multipleSelection.value.map((item: any) => item.id);
          await bulkRestoreUsers({ ids });
          message($t("Users restored successfully"), { type: "success" });
          fnGetUsers();
        } catch (error) {
          message($t("Failed to restore users"), { type: "error" });
        }
      })
      .catch(() => {
        message($t("Restore cancelled"), { type: "info" });
      });
  };

  /*
   ***************************
   *   Form Handlers
   ***************************
   */
  const handleSubmit = (values: FieldValues) => {
    if (values.id) {
      fnHandleUpdateUser(values.id, values);
    } else {
      fnHandleCreateUser(values);
    }
  };

  const handleFilter = (values: FieldValues) => {
    filterRef.value = { ...values };
    pagination.currentPage = 1;
    filterVisible.value = false;
    fnGetUsers();
  };

  /*
   ***************************
   *   Return Hook Interface
   ***************************
   */
  return {
    // Data/State
    loading,
    filterRef,
    pagination,
    records,
    multipleSelection,
    sort,
    filterVisible,
    drawerVisible,
    drawerValues,
    userFormRef,

    // API Handlers
    fnGetUsers,
    fnHandleCreateUser,
    fnHandleUpdateUser,
    fnHandleDelete,
    fnHandleBulkDelete,

    // Table Event Handlers
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandlePageChange,
    fnHandleSizeChange,

    // UI Action Handlers
    handleDelete,
    handleBulkDelete,
    handleDestroy,
    handleBulkDestroy,
    handleRestore,
    handleBulkRestore,

    // Form Handlers
    handleSubmit,
    handleFilter
  };
}
