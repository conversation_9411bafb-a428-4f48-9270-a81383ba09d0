<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, onMounted, ref, watch } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
  isEdit?: boolean;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
}>();

const loading = ref(false);

const formRef = ref();

onMounted(() => {});

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("Username")),
    prop: "username",
    valueType: "input",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input username"),
        trigger: ["blur"]
      }
    ],
    fieldProps: {
      placeholder: ""
    },
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("First Name")),
    prop: "firstName",
    valueType: "input",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input first name"),
        trigger: ["blur"]
      }
    ],
    fieldProps: {
      placeholder: ""
    },
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Last Name")),
    prop: "lastName",
    valueType: "input",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input last name"),
        trigger: ["blur"]
      }
    ],
    fieldProps: {
      placeholder: ""
    },
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Email")),
    prop: "email",
    valueType: "input",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input email"),
        trigger: ["blur"]
      },
      {
        type: "email",
        message: $t("Please input valid email"),
        trigger: ["blur"]
      }
    ],
    fieldProps: {
      placeholder: ""
    },
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Password")),
    prop: "password",
    valueType: "input",
    rules: [
      {
        required: true,
        message: $t("Please input password"),
        trigger: ["blur"]
      },
      {
        min: 6,
        message: $t("Password must be at least 6 characters"),
        trigger: ["blur"]
      }
    ],
    fieldProps: {
      placeholder: "",
      type: "password",
      showPassword: true
    },
    colProps: { span: 12 },
    hideInForm: computed(() => !!props.values?.id)
  },
  {
    label: computed(() => $t("Confirm Password")),
    prop: "passwordConfirmation",
    valueType: "input",
    rules: [
      {
        required: true,
        message: $t("Please confirm password"),
        trigger: ["blur"]
      },
      {
        validator: (rule: any, value: string, callback: Function) => {
          if (value !== props.values?.password) {
            callback(new Error($t("Passwords do not match")));
          } else {
            callback();
          }
        },
        trigger: ["blur"]
      }
    ],
    fieldProps: {
      placeholder: "",
      type: "password",
      showPassword: true
    },
    colProps: { span: 12 },
    hideInForm: computed(() => !!props.values?.id)
  },
  {
    label: computed(() => $t("Birthday")),
    prop: "birthday",
    valueType: "date-picker",
    fieldProps: {
      placeholder: "",
      type: "date",
      format: "YYYY-MM-DD",
      valueFormat: "YYYY-MM-DD"
    },
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Gender")),
    prop: "gender",
    valueType: "select",
    options: [
      { label: $t("Male"), value: "male" },
      { label: $t("Female"), value: "female" },
      { label: $t("Other"), value: "other" }
    ],
    fieldProps: {
      placeholder: ""
    },
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Phone")),
    prop: "phone",
    valueType: "input",
    fieldProps: {
      placeholder: ""
    },
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Address")),
    prop: "address",
    valueType: "textarea",
    fieldProps: {
      placeholder: "",
      rows: 3
    },
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Status")),
    prop: "status",
    valueType: "select",
    required: true,
    options: [
      { label: $t("Active"), value: "active" },
      { label: $t("Inactive"), value: "inactive" },
      { label: $t("Suspended"), value: "suspended" },
      { label: $t("Banned"), value: "banned" },
      { label: $t("Pending"), value: "pending" }
    ],
    fieldProps: {
      placeholder: "",
      clearable: false
    },
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Email Verified")),
    prop: "isVerified",
    valueType: "switch",
    fieldProps: {
      activeText: $t("Verified"),
      inactiveText: $t("Not Verified")
    },
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Newsletter Subscribed")),
    prop: "newsletterSubscribed",
    valueType: "switch",
    fieldProps: {
      activeText: $t("Subscribed"),
      inactiveText: $t("Not Subscribed")
    },
    colProps: { span: 12 }
  }
];

const handleSubmit = async (values: FieldValues) => {
  if (!formRef.value?.formInstance) return;
  const valid = await formRef.value.formInstance.validate();
  if (!valid) return;

  try {
    loading.value = true;
    emit("submit", values);
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 3000); // delay 3 seconds
  }
};

const resetForm = () => {
  if (formRef.value?.formInstance) {
    formRef.value.formInstance.resetFields();
  }
};

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="40%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
    @close="resetForm"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">
          {{ $t("Information Form") }}
        </span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="emit('update:visible', false)">
          {{ $t("Cancel") }}
        </el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleSubmit(values)"
        >
          {{ $t("Submit") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>
