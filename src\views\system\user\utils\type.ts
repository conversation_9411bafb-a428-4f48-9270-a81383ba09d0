export type FormItemProps = {
  id?: number | null;
  name?: string;
  email?: string;
  password?: string;
  passwordConfirmation?: string;
  avatar?: string;
  phone?: string;
  address?: string;
  roleId?: number;
  isActive?: boolean;
  status?: string;
  emailVerifiedAt?: string;
  createdAt?: string;
  updatedAt?: string;
};

export type UserFilterProps = {
  name?: string;
  email?: string;
  roleId?: number;
  isActive?: boolean;
  status?: string;
  isTrashed?: "yes" | "no";
  [key: string]: any;
};
